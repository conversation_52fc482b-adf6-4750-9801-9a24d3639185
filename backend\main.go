package main

import (
	"bufio"
	"context"
	"encoding/base64"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"strings"
	"sync"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"

	"vulnerability_push/internal/config"
	"vulnerability_push/internal/database"
	"vulnerability_push/internal/handlers"
	"vulnerability_push/internal/models"
	"vulnerability_push/internal/scheduler"
	"vulnerability_push/internal/service"
	"vulnerability_push/internal/utils"
	"vulnerability_push/push"
)

var (
	sensitiveWords     []string
	sensitiveWordsOnce sync.Once
	sensitiveWordsMu   sync.RWMutex
)

// App 应用程序结构
type App struct {
	config              *config.Config
	dbManager           *database.Manager
	router              *gin.Engine
	logger              utils.Logger
	server              *http.Server
	dataScheduler       *scheduler.DataInterfaceScheduler
	strategyScheduler   *scheduler.ProductionStrategyScheduler
}

func main() {
	// 创建应用实例
	app := &App{}
	
	// 初始化应用
	if err := app.Initialize(); err != nil {
		log.Fatalf("应用初始化失败: %v", err)
	}
	
	// 启动应用
	if err := app.Run(); err != nil {
		log.Fatalf("应用启动失败: %v", err)
	}
}

// Initialize 初始化应用
func (app *App) Initialize() error {
	// 1. 加载配置
	configPath := "config.yaml"
	if envPath := os.Getenv("CONFIG_PATH"); envPath != "" {
		configPath = envPath
	}
	
	var err error
	app.config, err = config.LoadConfig(configPath)
	if err != nil {
		return fmt.Errorf("加载配置失败: %w", err)
	}
	
	// 2. 初始化日志
	if err := app.initLogger(); err != nil {
		return fmt.Errorf("初始化日志失败: %w", err)
	}
	
	app.logger.Infof("应用配置加载成功，模式: %s", app.config.Server.Mode)
	
	// 3. 初始化数据库
	if err := app.initDatabase(); err != nil {
		return fmt.Errorf("初始化数据库失败: %w", err)
	}

	// 4. 初始化IP地理位置查询器
	if err := app.initIPSearcher(); err != nil {
		return fmt.Errorf("初始化IP查询器失败: %w", err)
	}

	// 5. 初始化路由
	if err := app.initRouter(); err != nil {
		return fmt.Errorf("初始化路由失败: %w", err)
	}

	// 6. 初始化调度器
	if err := app.initScheduler(); err != nil {
		return fmt.Errorf("初始化调度器失败: %w", err)
	}

	// 7. 测试ES告警接口（如果环境变量设置了）
	if os.Getenv("TEST_ES_ALARM") == "true" {
		app.logger.Infof("开始测试ES告警接口...")
		if err := app.testESAlarmInterface(); err != nil {
			app.logger.Errorf("ES告警接口测试失败: %v", err)
		} else {
			app.logger.Infof("ES告警接口测试完成")
		}
	}

	app.logger.Infof("应用初始化完成")
	return nil
}

// initLogger 初始化日志
func (app *App) initLogger() error {
	logConfig := app.config.Log
	
	// 解析日志级别
	level := utils.ParseLogLevel(logConfig.Level)
	
	var logger utils.Logger
	
	if logConfig.Console && logConfig.File != "" {
		// 同时输出到控制台和文件（使用轮转日志）
		consoleLogger := utils.NewConsoleLogger(level, logConfig.ShowFile)
		fileLogger, err := utils.NewRotatingFileLogger(logConfig.File, level, logConfig.ShowFile)
		if err != nil {
			return fmt.Errorf("创建轮转文件日志记录器失败: %w", err)
		}
		logger = utils.NewMultiLogger(consoleLogger, fileLogger)
	} else if logConfig.File != "" {
		// 只输出到文件（使用轮转日志）
		var err error
		logger, err = utils.NewRotatingFileLogger(logConfig.File, level, logConfig.ShowFile)
		if err != nil {
			return fmt.Errorf("创建轮转文件日志记录器失败: %w", err)
		}
	} else {
		// 只输出到控制台
		logger = utils.NewConsoleLogger(level, logConfig.ShowFile)
	}
	
	app.logger = logger
	utils.SetDefaultLogger(logger)
	
	return nil
}

// initDatabase 初始化数据库
func (app *App) initDatabase() error {
	// 创建数据库配置
	dbConfig := &database.DatabaseConfig{
		Type:   app.config.Database.Type,
		MySQL:  database.MySQLConfig(app.config.Database.MySQL),
		SQLite: database.SQLiteConfig(app.config.Database.SQLite),
	}

	// 创建数据库管理器
	app.dbManager = database.NewManager(dbConfig, app.logger)

	// 初始化数据库
	allModels := models.GetAllModels()
	if err := app.dbManager.Initialize(allModels, false); err != nil {
		return fmt.Errorf("数据库初始化失败: %w", err)
	}

	app.logger.Infof("数据库初始化成功")

	// 初始化IOC白名单缓存
	app.initWhitelistCache()

	// 初始化推送模块
	app.initPushModule()

	return nil
}

// initWhitelistCache 初始化IOC白名单缓存
func (app *App) initWhitelistCache() {
	app.logger.Infof("初始化IOC白名单缓存...")
	service.InitGlobalWhitelistCache(app.dbManager.GetDB())
	app.logger.Infof("IOC白名单缓存初始化完成")

	// 初始化威胁情报管理器
	app.logger.Infof("初始化威胁情报管理器...")
	service.InitGlobalThreatIntelligenceManager(app.dbManager.GetDB())
	app.logger.Infof("威胁情报管理器初始化完成")
}

// initPushModule 初始化推送模块
func (app *App) initPushModule() {
	// 设置推送模块的数据库实例
	push.DB = app.dbManager.GetDB()

	// push模块现在直接使用utils日志，无需单独设置

	// 加载敏感词字典
	app.logger.Infof("加载敏感词字典...")
	if err := LoadSensitiveWords(); err != nil {
		app.logger.Warnf("警告: 加载敏感词字典失败: %v", err)

		// 尝试创建一个默认的敏感词文件
		defaultWords := []string{
			"敏感词1", "敏感词2",
		}

		if err := EncodeSensitiveWords(defaultWords, "sensitive_words.txt"); err != nil {
			app.logger.Errorf("创建默认敏感词文件失败: %v", err)
		} else {
			app.logger.Infof("已创建默认敏感词文件")
			// 重新加载敏感词
			sensitiveWordsOnce = sync.Once{}
			if err := LoadSensitiveWords(); err != nil {
				app.logger.Errorf("重新加载敏感词失败: %v", err)
			}
		}
	}

	// 设置敏感词检查函数（使用真实的敏感词检查）
	push.ContainsSensitiveWords = ContainsSensitiveWords

	app.logger.Infof("推送模块初始化成功")
}

// initIPSearcher 初始化IP地理位置查询器
func (app *App) initIPSearcher() error {
	if err := utils.InitGlobalIPSearcher(); err != nil {
		app.logger.Warnf("初始化IP查询器失败: %v", err)
		// 不返回错误，允许应用继续运行
	} else {
		app.logger.Infof("IP地理位置查询器初始化成功")
	}

	// 初始化威胁情报服务
	if err := service.InitGlobalServices(app.dbManager.GetDB()); err != nil {
		app.logger.Warnf("初始化威胁情报服务失败: %v", err)
		// 不返回错误，允许应用继续运行
	} else {
		app.logger.Infof("威胁情报服务初始化成功")
	}

	return nil
}

// initRouter 初始化路由
func (app *App) initRouter() error {
	// 设置Gin模式
	if app.config.IsReleaseMode() {
		gin.SetMode(gin.ReleaseMode)
	} else if app.config.IsDebugMode() {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.TestMode)
	}
	
	// 创建路由管理器
	routerManager := handlers.NewRouterManager(
		app.dbManager.GetDB(),
		app.config.Security.JWTSecret,
		int(app.config.Security.JWTExpireHour),
	)
	app.router = routerManager.SetupRoutes()
	
	app.logger.Infof("路由初始化成功")
	return nil
}

// initScheduler 初始化调度器
func (app *App) initScheduler() error {
	db := app.dbManager.GetDB()

	// 初始化数据接口调度器
	app.dataScheduler = scheduler.NewDataInterfaceScheduler(db)

	// 初始化生产策略调度器
	app.strategyScheduler = scheduler.NewProductionStrategyScheduler(db)

	app.logger.Infof("调度器初始化成功")
	return nil
}

// testESAlarmInterface 测试ES告警接口
func (app *App) testESAlarmInterface() error {
	db := app.dbManager.GetDB()

	// 创建数据接口处理器
	handler := handlers.NewDataInterfaceHandler(db)

	// 创建ES告警接口实例
	esInterface := handlers.NewESAlarmInterface(handler)

	app.logger.Infof("=== ES告警数据接口测试开始 ===")

	// 1. 显示接口信息
	app.logger.Infof("接口类型: %s", esInterface.GetType())
	app.logger.Infof("接口描述: %s", esInterface.GetDescription())

	// 2. 测试配置验证
	app.logger.Infof("--- 配置验证测试 ---")

	// 有效配置
	validConfig := map[string]interface{}{
		"es_url": "http://127.0.0.1:9200",
	}

	err := esInterface.ValidateConfig(validConfig)
	if err != nil {
		app.logger.Errorf("配置验证失败: %v", err)
	} else {
		app.logger.Infof("配置验证通过")
	}

	// 3. 测试索引生成
	app.logger.Infof("--- 索引测试 ---")
	testIndex := "alarm_info2025-07-23"
	app.logger.Infof("测试索引: %s", testIndex)
	app.logger.Infof("注意: 当前使用固定测试索引，后续将改为自动使用当前日期")

	// 4. 测试来源标签映射
	app.logger.Infof("--- 来源标签映射测试 ---")
	testNodeCodes := []string{"2013", "2003", "2001", "9999"}
	for _, nodeCode := range testNodeCodes {
		label := esInterface.GetESSourceLabel(nodeCode)
		app.logger.Infof("节点代码 %s -> %s", nodeCode, label)
	}

	// 5. 测试海事局内网IP检查
	app.logger.Infof("--- 海事局内网IP检查测试 ---")
	testIPs := []string{
		"**********",   // 海事局内网
		"**********",   // 海事局内网
		"**********",   // 海事局内网
		"*********",    // 不在范围内
		"**********",   // 不在范围内
		"***********",  // 普通内网
		"*******",      // 外网
	}

	for _, ip := range testIPs {
		isMaritimeInternal := esInterface.IsMaritimeInternalIP(ip)
		app.logger.Infof("IP %s 是否为海事局内网: %t", ip, isMaritimeInternal)
	}

	// 6. 测试事件类型和级别转换
	app.logger.Infof("--- 事件类型和级别转换测试 ---")

	// 事件类型转换测试
	incidentTypes := []interface{}{
		"web_attack",
		20101.0,
		20102,
		nil,
	}

	for _, incidentType := range incidentTypes {
		converted := esInterface.ConvertIncidentTypeSub(incidentType)
		app.logger.Infof("事件类型 %v -> %s", incidentType, converted)
	}

	// 事件级别转换测试
	incidentLevels := []interface{}{
		1.0,
		2.0,
		3.0,
		4.0,
		"高危",
		nil,
	}

	for _, level := range incidentLevels {
		converted := esInterface.ConvertIncidentLevel(level)
		app.logger.Infof("事件级别 %v -> %s", level, converted)
	}

	// 7. 测试威胁评分计算
	app.logger.Infof("--- 威胁评分计算测试 ---")
	testCases := []struct {
		levels      map[string]int
		attackCount int
	}{
		{map[string]int{"严重": 1}, 1},
		{map[string]int{"高危": 1}, 10},
		{map[string]int{"中危": 1}, 50},
		{map[string]int{"低危": 1}, 100},
	}

	for _, tc := range testCases {
		score := esInterface.CalculateThreatScore(tc.levels, tc.attackCount)
		app.logger.Infof("级别: %v, 攻击次数: %d -> 威胁评分: %.2f", tc.levels, tc.attackCount, score)
	}

	// 8. 检查是否已存在测试数据接口记录
	app.logger.Infof("--- 检查测试数据接口记录 ---")

	var existingInterface models.DataInterface
	result := db.Where("name = ? AND type = ?", "ES告警数据接口", "es_alarm").First(&existingInterface)

	if result.Error == nil {
		// 已存在，跳过创建
		app.logger.Infof("ES告警接口已存在，ID: %d，跳过创建", existingInterface.ID)
	} else {
		// 不存在，创建新的
		app.logger.Infof("创建ES告警接口...")

		dataInterface := &models.DataInterface{
			Name:        "ES告警数据接口",
			Type:        "es_alarm",
			Description: "用于采集的ES告警数据接口",
			Config:      `{"es_url":"http://127.0.0.1:9200"}`,
			Status:      "enabled",
			Interval:    3600, // 1小时
			CreatedAt:   time.Now().Unix(),
			UpdatedAt:   time.Now().Unix(),
		}

		// 保存到数据库
		createResult := db.Create(dataInterface)
		if createResult.Error != nil {
			app.logger.Errorf("保存数据接口失败: %v", createResult.Error)
		} else {
			app.logger.Infof("创建数据接口成功，ID: %d", dataInterface.ID)

			// 创建执行日志
			logEntry := &models.DataInterfaceLog{
				InterfaceID: dataInterface.ID,
				Status:      "success",
				Message:     "ES告警接口测试完成",
				ProcessedAt: time.Now().Unix(),
			}

			logResult := db.Create(logEntry)
			if logResult.Error != nil {
				app.logger.Errorf("创建执行日志失败: %v", logResult.Error)
			} else {
				app.logger.Infof("创建执行日志成功，ID: %d", logEntry.ID)
			}
		}
	}

	app.logger.Infof("=== ES告警数据接口测试完成 ===")
	app.logger.Infof("注意：实际使用时需要确保Elasticsearch服务正在运行，并且有相应的告警数据索引。")
	app.logger.Infof("配置示例: {\"es_url\":\"http://127.0.0.1:9200\"}")

	return nil
}

// Run 运行应用
func (app *App) Run() error {
	// 创建HTTP服务器
	app.server = &http.Server{
		Addr:           app.config.GetAddress(),
		Handler:        app.router,
		ReadTimeout:    time.Duration(app.config.Server.ReadTimeout) * time.Second,
		WriteTimeout:   time.Duration(app.config.Server.WriteTimeout) * time.Second,
		MaxHeaderBytes: int(app.config.Server.MaxBodySize),
	}
	
	// 启动调度器
	if app.dataScheduler != nil {
		app.dataScheduler.Start()
	}
	if app.strategyScheduler != nil {
		app.strategyScheduler.Start()
	}

	// 启动服务器
	go func() {
		app.logger.Infof("服务器启动在 %s", app.config.GetAddress())
		if err := app.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			app.logger.Errorf("服务器启动失败: %v", err)
			os.Exit(1)
		}
	}()
	
	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	
	app.logger.Infof("正在关闭服务器...")
	
	// 优雅关闭
	return app.Shutdown()
}

// Shutdown 优雅关闭应用
func (app *App) Shutdown() error {
	// 创建超时上下文
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	// 停止调度器
	if app.dataScheduler != nil {
		app.dataScheduler.Stop()
	}
	if app.strategyScheduler != nil {
		app.strategyScheduler.Stop()
	}

	// 关闭HTTP服务器
	if err := app.server.Shutdown(ctx); err != nil {
		app.logger.Errorf("服务器关闭失败: %v", err)
		return err
	}
	
	// 关闭IP查询器
	if err := utils.CloseGlobalIPSearcher(); err != nil {
		app.logger.Warnf("IP查询器关闭失败: %v", err)
	}

	// 关闭数据库连接
	if app.dbManager != nil {
		if err := app.dbManager.Close(); err != nil {
			app.logger.Errorf("数据库关闭失败: %v", err)
			return err
		}
	}
	
	app.logger.Infof("应用已优雅关闭")
	return nil
}

// Health 健康检查
func (app *App) Health() error {
	// 检查数据库连接
	if app.dbManager != nil {
		if err := app.dbManager.Health(); err != nil {
			return fmt.Errorf("数据库健康检查失败: %w", err)
		}
	}
	
	return nil
}

// LoadSensitiveWords 加载敏感词字典
// 使用sync.Once确保只加载一次
func LoadSensitiveWords() error {
	var loadErr error

	sensitiveWordsOnce.Do(func() {
		filePath := "sensitive_words.txt"
		file, err := os.Open(filePath)
		if err != nil {
			loadErr = fmt.Errorf("无法打开敏感词字典文件: %w", err)
			return
		}
		defer file.Close()

		words := make([]string, 0, 100) // 预分配合理容量
		scanner := bufio.NewScanner(file)
		for scanner.Scan() {
			encodedWord := strings.TrimSpace(scanner.Text())
			if encodedWord == "" || encodedWord[0] == '#' { // 忽略空行和注释行
				continue
			}

			// 解码Base64编码的敏感词
			decodedBytes, err := base64.StdEncoding.DecodeString(encodedWord)
			if err != nil {
				utils.Warnf("解码敏感词失败: %v, 原词: %s", err, encodedWord)
				continue
			}

			word := strings.TrimSpace(string(decodedBytes))
			if word != "" {
				words = append(words, word)
			}
		}

		if err := scanner.Err(); err != nil {
			loadErr = fmt.Errorf("读取敏感词字典文件出错: %w", err)
			return
		}

		sensitiveWordsMu.Lock()
		sensitiveWords = words
		sensitiveWordsMu.Unlock()

		utils.Infof("已加载 %d 个敏感词", len(words))
	})

	return loadErr
}

// EncodeSensitiveWords 将敏感词编码为Base64并写入文件
// 已编码的词会被保留，只编码未编码的词
func EncodeSensitiveWords(words []string, filePath string) error {
	file, err := os.Create(filePath)
	if err != nil {
		return fmt.Errorf("无法创建敏感词字典文件: %w", err)
	}
	defer file.Close()

	writer := bufio.NewWriter(file)

	// 写入文件头注释
	writer.WriteString("# 敏感词字典 - Base64编码\n")
	writer.WriteString("# 每行一个编码后的敏感词\n\n")

	var encodedCount, alreadyEncodedCount int

	for _, word := range words {
		if word = strings.TrimSpace(word); word == "" || word[0] == '#' {
			// 保留注释行
			if word != "" {
				writer.WriteString(word + "\n")
			}
			continue
		}

		// 检查是否已经是Base64编码
		if isBase64Encoded(word) {
			// 已编码，直接写入
			writer.WriteString(word + "\n")
			alreadyEncodedCount++
		} else {
			// 未编码，进行编码后写入
			encoded := base64.StdEncoding.EncodeToString([]byte(word))
			writer.WriteString(encoded + "\n")
			encodedCount++
		}
	}

	utils.Infof("编码完成: %d个词已编码, %d个词保持原编码", encodedCount, alreadyEncodedCount)

	return writer.Flush()
}

// isBase64Encoded 检查字符串是否为Base64编码
func isBase64Encoded(s string) bool {
	// 简单检查：Base64字符串只包含A-Z, a-z, 0-9, +, /, =
	// 且长度是4的倍数
	if len(s)%4 != 0 {
		return false
	}

	for _, c := range s {
		if !((c >= 'A' && c <= 'Z') || (c >= 'a' && c <= 'z') ||
			 (c >= '0' && c <= '9') || c == '+' || c == '/' || c == '=') {
			return false
		}
	}

	// 尝试解码验证
	_, err := base64.StdEncoding.DecodeString(s)
	return err == nil
}

// ContainsSensitiveWords 检查文本是否包含敏感词
// 返回是否包含敏感词以及找到的敏感词列表
func ContainsSensitiveWords(text string) (bool, []string) {
	if text == "" {
		return false, nil
	}

	sensitiveWordsMu.RLock()
	localWords := sensitiveWords
	sensitiveWordsMu.RUnlock()

	if len(localWords) == 0 {
		// 如果敏感词列表为空，尝试加载
		err := LoadSensitiveWords()
		if err != nil {
			utils.Errorf("加载敏感词失败: %v", err)
			return false, nil
		}

		sensitiveWordsMu.RLock()
		localWords = sensitiveWords
		sensitiveWordsMu.RUnlock()

		if len(localWords) == 0 {
			return false, nil
		}
	}

	// 预分配合理容量
	foundWords := make([]string, 0, 5)

	// 转换为小写以进行不区分大小写的比较
	lowerText := strings.ToLower(text)

	for _, word := range localWords {
		if strings.Contains(lowerText, strings.ToLower(word)) {
			foundWords = append(foundWords, word)
		}
	}

	return len(foundWords) > 0, foundWords
}
