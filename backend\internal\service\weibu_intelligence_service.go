package service

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"gorm.io/gorm"
	"vulnerability_push/internal/models"
	"vulnerability_push/internal/utils"
)

// =============================================================================
// 微步威胁情报客户端相关结构体
// =============================================================================

// WeibuClient 微步威胁情报请求客户端
type WeibuClient struct {
	host    string // API域名，如 api.threatbook.cn
	apiKey  string // API密钥
	timeout time.Duration
	client  *http.Client
}

// WeibuIPReputationResponse 微步IP信誉查询响应结构
type WeibuIPReputationResponse struct {
	ResponseCode int                    `json:"response_code"` // 响应状态码：0成功，其他为错误
	VerboseMsg   string                 `json:"verbose_msg"`   // 响应消息
	Data         map[string]interface{} `json:"data"`          // 响应数据，IP为key的映射
}

// WeibuIPReputationRecord IP信誉记录结构
type WeibuIPReputationRecord struct {
	IsMalicious      string                 `json:"is_malicious"`      // 是否恶意
	ConfidenceLevel  string                 `json:"confidence_level"`  // 可信度
	Severity         string                 `json:"severity"`          // 严重程度
	Judgments        []string               `json:"judgments"`         // 威胁类型
	TagsClasses      []WeibuTagClass        `json:"tags_classes"`      // 标签类别
	Basic            WeibuBasicInfo         `json:"basic"`             // 基础信息
	UpdateTime       string                 `json:"update_time"`       // 更新时间
}

// WeibuTagClass 标签类别结构
type WeibuTagClass struct {
	TagsType string      `json:"tags_type"` // 标签类别
	Tags     interface{} `json:"tags"`      // 标签信息（可能是数组或字符串）
}

// WeibuBasicInfo 基础信息结构
type WeibuBasicInfo struct {
	Location WeibuLocation `json:"location"` // 位置信息
	Carrier  string        `json:"carrier"`  // 运营商
}

// WeibuLocation 位置信息结构
type WeibuLocation struct {
	Country  string `json:"country"`  // 国家
	Province string `json:"province"` // 省份
	City     string `json:"city"`     // 城市
}

// WeibuIntelligenceResult 微步威胁情报查询结果
type WeibuIntelligenceResult struct {
	Success      bool        `json:"success"`
	Data         interface{} `json:"data"`
	ErrorMessage string      `json:"errorMessage,omitempty"`
	QueryTime    int64       `json:"queryTime"`
}

// WeibuIntelligenceService 微步威胁情报服务
type WeibuIntelligenceService struct {
	db     *gorm.DB
	client *WeibuClient
}

// WeibuQueryRequest 微步威胁情报查询请求
type WeibuQueryRequest struct {
	IOC   string `json:"ioc" binding:"required"`   // IOC值（目前仅支持IP）
	IOCId uint   `json:"iocId,omitempty"`          // IOC情报ID（可选，用于更新数据库记录）
}

// WeibuQueryResponse 微步威胁情报查询响应
type WeibuQueryResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// =============================================================================
// 微步威胁情报客户端方法
// =============================================================================

// NewWeibuClient 创建新的微步威胁情报客户端
func NewWeibuClient(host, apiKey string) *WeibuClient {
	return &WeibuClient{
		host:   host,
		apiKey: apiKey,
		timeout: 10 * time.Second, // 默认10秒超时
		client: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

// SetTimeout 设置请求超时时间
func (c *WeibuClient) SetTimeout(timeout time.Duration) *WeibuClient {
	c.timeout = timeout
	c.client.Timeout = timeout
	return c
}

// QueryIPReputationBatch 批量查询IP信誉（参考Python代码实现）
func (c *WeibuClient) QueryIPReputationBatch(ctx context.Context, ipList []string, lang string) (*WeibuIPReputationResponse, error) {
	// 参数验证
	if c.apiKey == "" {
		return nil, fmt.Errorf("请填写API密钥")
	}
	if len(ipList) == 0 {
		return nil, fmt.Errorf("IP列表不能为空")
	}
	if len(ipList) > 100 {
		return nil, fmt.Errorf("批量查询最多支持100个IP")
	}

	// 构建请求URL
	requestURL := fmt.Sprintf("https://%s/v3/scene/ip_reputation", c.host)

	// 构建查询参数（参考Python代码的params）
	params := url.Values{}
	params.Set("apikey", c.apiKey)
	params.Set("resource", strings.Join(ipList, ",")) // 批量查询多个IP，用逗号分隔
	if lang != "" {
		params.Set("lang", lang) // 可选，返回中文结果
	}

	// 创建GET请求（参考Python代码使用GET方法）
	fullURL := fmt.Sprintf("%s?%s", requestURL, params.Encode())
	req, err := http.NewRequestWithContext(ctx, "GET", fullURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 调试输出请求信息
	utils.Infof("微步请求URL: %s\n", fullURL)
	utils.Infof("微步请求方法: GET\n")
	utils.Infof("微步查询IP数量: %d\n", len(ipList))
	utils.Infof("微步查询IP列表: %v\n", ipList)

	// 执行请求，支持重试（参考天际友盟的重试逻辑）
	var resp *http.Response
	for i := 0; i < 3; i++ {
		resp, err = c.client.Do(req)
		if err != nil {
			if i == 2 { // 最后一次重试
				return nil, fmt.Errorf("发送HTTP请求失败: %v", err)
			}
			continue
		}

		// 检查状态码
		if resp.StatusCode == 200 {
			break
		} else if resp.StatusCode >= 500 {
			resp.Body.Close()
			if i == 2 { // 最后一次重试
				return nil, fmt.Errorf("服务器错误，状态码: %d", resp.StatusCode)
			}
			continue
		} else {
			// 客户端错误，不重试
			break
		}
	}

	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应体失败: %v", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("API返回错误状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析响应数据
	var response WeibuIPReputationResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析响应数据失败: %v, 响应内容: %s", err, string(body))
	}

	// 检查业务状态码（微步API成功状态码为0）
	if response.ResponseCode != 0 {
		return nil, fmt.Errorf("API返回业务错误: code=%d, message=%s", response.ResponseCode, response.VerboseMsg)
	}

	return &response, nil
}

// NewWeibuIntelligenceService 创建微步威胁情报服务（使用默认配置）
func NewWeibuIntelligenceService(db *gorm.DB) *WeibuIntelligenceService {
	// 使用默认配置创建客户端
	client := NewWeibuClient("api.threatbook.cn", "your_api_key_here")
	client.SetTimeout(10 * time.Second)

	return &WeibuIntelligenceService{
		db:     db,
		client: client,
	}
}

// NewWeibuIntelligenceServiceWithConfig 创建微步威胁情报服务（使用指定配置）
func NewWeibuIntelligenceServiceWithConfig(db *gorm.DB, host, apiKey string) *WeibuIntelligenceService {
	// 如果配置为空，使用默认值
	if host == "" {
		host = "api.threatbook.cn"
	}
	if apiKey == "" {
		apiKey = "your_api_key_here"
	}

	client := NewWeibuClient(host, apiKey)
	client.SetTimeout(10 * time.Second)

	return &WeibuIntelligenceService{
		db:     db,
		client: client,
	}
}

// QueryIOC 查询单个IOC的威胁情报（目前仅支持IP）
func (s *WeibuIntelligenceService) QueryIOC(req WeibuQueryRequest) (*WeibuQueryResponse, error) {
	// 创建上下文
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()

	// 调用微步威胁情报查询服务（批量查询单个IP）
	response, err := s.client.QueryIPReputationBatch(ctx, []string{req.IOC}, "zh")
	if err != nil {
		errorMsg := fmt.Sprintf("查询微步威胁情报失败: %v", err)

		// 如果提供了IOC ID，更新数据库记录为错误状态
		if req.IOCId > 0 {
			if updateErr := s.updateIOCRecord(req.IOCId, nil, errorMsg); updateErr != nil {
				utils.Errorf("更新IOC情报微步数据失败: %v", updateErr)
			}
		}

		return &WeibuQueryResponse{
			Success: false,
			Message: errorMsg,
		}, err
	}

	// 从批量结果中提取单个IP的结果
	var ipData interface{}
	if response.Data != nil {
		if data, exists := response.Data[req.IOC]; exists {
			ipData = data
		}
	}

	if ipData == nil {
		errorMsg := "未找到该IP的信誉信息"

		// 如果提供了IOC ID，更新数据库记录为错误状态
		if req.IOCId > 0 {
			if updateErr := s.updateIOCRecord(req.IOCId, nil, errorMsg); updateErr != nil {
				utils.Errorf("更新IOC情报微步数据失败: %v", updateErr)
			}
		}

		return &WeibuQueryResponse{
			Success: false,
			Message: errorMsg,
		}, fmt.Errorf(errorMsg)
	}

	// 创建结果对象
	weibuResult := &WeibuIntelligenceResult{
		Success:   true,
		Data:      ipData,
		QueryTime: time.Now().Unix(),
	}

	// 如果提供了IOC ID，更新数据库记录
	if req.IOCId > 0 {
		if updateErr := s.updateIOCRecord(req.IOCId, weibuResult, ""); updateErr != nil {
			// 记录错误但不影响返回结果
			fmt.Printf("更新IOC情报微步数据失败: %v\n", updateErr)
		}
	}

	return &WeibuQueryResponse{
		Success: true,
		Message: "查询成功",
		Data:    weibuResult.Data,
	}, nil
}

// updateIOCRecord 更新IOC记录的微步数据
func (s *WeibuIntelligenceService) updateIOCRecord(iocId uint, weibuResult *WeibuIntelligenceResult, errorMessage string) error {
	updateData := map[string]interface{}{
		"weibu_query_time": time.Now().Unix(),
	}

	if errorMessage != "" {
		updateData["weibu_query_status"] = "error"
		updateData["weibu_error_message"] = errorMessage
	} else {
		updateData["weibu_query_status"] = "success"
		updateData["weibu_error_message"] = ""

		if weibuResult != nil {
			if serializedData, err := weibuResult.SerializeData(); err == nil {
				updateData["weibu_data"] = serializedData
			} else {
				updateData["weibu_error_message"] = fmt.Sprintf("序列化微步数据失败: %v", err)
			}
		}
	}

	return s.db.Model(&models.IOCIntelligence{}).Where("id = ?", iocId).Updates(updateData).Error
}

// SerializeData 序列化数据为JSON字符串
func (r *WeibuIntelligenceResult) SerializeData() (string, error) {
	if r.Data == nil {
		return "", nil
	}

	dataBytes, err := json.Marshal(r.Data)
	if err != nil {
		return "", fmt.Errorf("序列化微步数据失败: %v", err)
	}

	return string(dataBytes), nil
}

// GetQueryStatus 获取查询状态
func (r *WeibuIntelligenceResult) GetQueryStatus() string {
	if r.Success {
		return "success"
	}
	return "failed"
}

// BatchQueryIPs 批量查询IP威胁情报
func (s *WeibuIntelligenceService) BatchQueryIPs(ips []string) (map[string]*WeibuIntelligenceResult, error) {
	if len(ips) == 0 {
		return nil, fmt.Errorf("IP列表为空")
	}

	results := make(map[string]*WeibuIntelligenceResult)

	// 创建上下文
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 按批次处理（微步API支持批量查询）
	batchSize := 100 // 微步API最多支持100个IP
	for i := 0; i < len(ips); i += batchSize {
		end := i + batchSize
		if end > len(ips) {
			end = len(ips)
		}

		batch := ips[i:end]

		// 调用微步API批量查询
		response, err := s.client.QueryIPReputationBatch(ctx, batch, "zh")
		if err != nil {
			// 为该批次的所有IP设置错误结果
			for _, ip := range batch {
				results[ip] = &WeibuIntelligenceResult{
					Success:      false,
					ErrorMessage: fmt.Sprintf("批量查询失败: %v", err),
					QueryTime:    time.Now().Unix(),
				}
			}
			continue
		}

		// 处理响应结果
		if response.Data != nil {
			for ip, record := range response.Data {
				results[ip] = &WeibuIntelligenceResult{
					Success:   true,
					Data:      record,
					QueryTime: time.Now().Unix(),
				}
			}
		}

		// 为批次中未返回结果的IP设置未找到状态
		for _, ip := range batch {
			if _, exists := results[ip]; !exists {
				results[ip] = &WeibuIntelligenceResult{
					Success:      false,
					ErrorMessage: "未找到该IP的信誉信息",
					QueryTime:    time.Now().Unix(),
				}
			}
		}

		// 添加延迟避免请求过于频繁
		if i+batchSize < len(ips) {
			time.Sleep(1 * time.Second)
		}
	}

	return results, nil
}

// BatchUpdateUnqueriedIOCs 批量更新未查询的IOC记录（仅处理IP类型）
func (s *WeibuIntelligenceService) BatchUpdateUnqueriedIOCs() (int, error) {
	// 获取所有未查询微步情报的IP类型IOC记录
	var iocList []models.IOCIntelligence
	if err := s.db.Where("(weibu_query_status = ? OR weibu_query_status IS NULL) AND ioc_type = ?", "not_queried", "ip").Find(&iocList).Error; err != nil {
		return 0, fmt.Errorf("获取IOC情报列表失败: %v", err)
	}

	if len(iocList) == 0 {
		return 0, nil
	}

	successCount := 0
	for _, ioc := range iocList {
		req := WeibuQueryRequest{
			IOC:   ioc.IOC,
			IOCId: ioc.ID,
		}

		_, err := s.QueryIOC(req)
		if err != nil {
			fmt.Printf("查询IOC %s 失败: %v\n", ioc.IOC, err)
		} else {
			successCount++
		}

		// 添加延迟避免请求过于频繁
		time.Sleep(100 * time.Millisecond)
	}

	return successCount, nil
}

// IsIPSupported 检查是否支持该IOC类型（微步目前仅支持IP）
func (s *WeibuIntelligenceService) IsIPSupported(iocType string) bool {
	return iocType == "ip"
}

// GetSupportedTypes 获取支持的IOC类型列表
func (s *WeibuIntelligenceService) GetSupportedTypes() []string {
	return []string{"ip"}
}
