<template>
  <div class="threat-intelligence-interface-container">
    <div class="filter-container">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="接口名称">
          <el-input
            v-model="filterForm.name"
            placeholder="输入接口名称"
            clearable
            prefix-icon="Search"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="接口类型">
          <el-select
            v-model="filterForm.type"
            placeholder="全部"
            clearable
            style="width: 180px;"
          >
            <el-option label="天际友盟" value="tjun" />
            <el-option label="微步威胁情报" value="weibu" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="filterForm.status"
            placeholder="全部"
            clearable
          >
            <el-option label="启用" value="enabled" />
            <el-option label="禁用" value="disabled" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :icon="Search">
            搜索
          </el-button>
          <el-button @click="resetFilter" :icon="RefreshLeft">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="action-container">
      <el-button type="primary" @click="handleCreate" :icon="Plus">
        新建威胁情报接口
      </el-button>
      <el-button @click="refreshList" :icon="Refresh">
        刷新
      </el-button>
      <el-button 
        type="danger" 
        @click="handleBatchDelete" 
        :disabled="multipleSelection.length === 0"
        :icon="Delete"
      >
        批量删除
      </el-button>
    </div>

    <el-table
      :data="threatIntelligenceInterfaces"
      style="width: 100%"
      v-loading="loading"
      @selection-change="handleSelectionChange"
      border
      stripe
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="name" label="接口名称" width="200" />
      <el-table-column prop="type" label="接口类型" width="120">
        <template #default="scope">
          <el-tag :type="getTypeTagType(scope.row.type)">
            {{ getTypeText(scope.row.type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="description" label="描述" show-overflow-tooltip />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status === 'enabled' ? 'success' : 'danger'">
            {{ scope.row.status === 'enabled' ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="创建时间" width="180">
        <template #default="scope">
          {{ formatTime(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="300" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="handleEdit(scope.row)" :icon="Edit">
            编辑
          </el-button>
          <el-button size="small" type="success" @click="handleTest(scope.row)" :icon="Connection">
            测试
          </el-button>
          <el-button 
            size="small" 
            :type="scope.row.status === 'enabled' ? 'warning' : 'success'"
            @click="handleToggleStatus(scope.row)"
            :icon="scope.row.status === 'enabled' ? VideoPause : VideoPlay"
          >
            {{ scope.row.status === 'enabled' ? '禁用' : '启用' }}
          </el-button>
          <el-button size="small" type="danger" @click="handleDelete(scope.row)" :icon="Delete">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新建/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="接口名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入接口名称" />
        </el-form-item>
        
        <el-form-item label="接口类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择接口类型" @change="handleTypeChange" style="width: 100%;">
            <el-option label="天际友盟" value="tjun" />
            <el-option label="微步威胁情报" value="weibu" />
          </el-select>
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入接口描述"
          />
        </el-form-item>

        <!-- 天际友盟配置 -->
        <template v-if="form.type === 'tjun'">
          <el-form-item label="API域名" prop="config.host">
            <el-input v-model="form.config.host" placeholder="例如: api.tj-un.com" />
          </el-form-item>
          <el-form-item label="APP KEY" prop="config.app_key">
            <el-input v-model="form.config.app_key" placeholder="请输入APP KEY" />
          </el-form-item>
          <el-form-item label="APP密钥" prop="config.app_secret">
            <el-input v-model="form.config.app_secret" placeholder="请输入APP密钥" type="password" show-password />
          </el-form-item>
          <el-form-item label="用户Token" prop="config.token">
            <el-input v-model="form.config.token" placeholder="请输入用户身份标识Token" />
          </el-form-item>
          <el-form-item label="超时时间(秒)" prop="config.timeout">
            <el-input-number v-model="form.config.timeout" :min="1" :max="300" placeholder="请输入超时时间" />
          </el-form-item>
        </template>

        <!-- 微步威胁情报配置 -->
        <template v-if="form.type === 'weibu'">
          <el-form-item label="API域名" prop="config.host">
            <el-input v-model="form.config.host" placeholder="例如: api.threatbook.cn" />
          </el-form-item>
          <el-form-item label="API密钥" prop="config.api_key">
            <el-input v-model="form.config.api_key" placeholder="请输入API密钥" type="password" show-password />
          </el-form-item>
          <el-form-item label="超时时间(秒)" prop="config.timeout">
            <el-input-number v-model="form.config.timeout" :min="1" :max="300" placeholder="请输入超时时间" />
          </el-form-item>
        </template>

        <el-form-item label="状态">
          <el-switch
            v-model="form.enabled"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ dialogTitle.includes('新建') ? '创建' : '更新' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 测试连接对话框 -->
    <el-dialog
      v-model="testDialogVisible"
      title="测试威胁情报接口连接"
      width="700px"
      destroy-on-close
    >
      <div class="test-container">
        <div class="test-info">
          <p><strong>接口名称:</strong> {{ currentTestInterface?.name }}</p>
          <p><strong>接口类型:</strong> {{ getTypeText(currentTestInterface?.type) }}</p>
        </div>

        <el-form :model="testForm" label-width="100px">
          <el-form-item label="测试IOC">
            <el-input v-model="testForm.ioc" placeholder="请输入要测试的IOC值（IP、域名等）" />
          </el-form-item>
          <el-form-item label="IOC类型">
            <el-select v-model="testForm.ioc_type" placeholder="请选择IOC类型" style="width: 100%;">
              <el-option label="IP地址" value="ip" />
              <el-option label="域名" value="domain" />
              <el-option label="URL" value="url" />
              <el-option label="文件哈希" value="hash" />
            </el-select>
          </el-form-item>
        </el-form>

        <div class="test-actions">
          <el-button type="primary" @click="executeTest" :loading="testLoading">
            执行测试
          </el-button>
        </div>

        <div v-if="testResult" class="test-result">
          <h4>测试结果:</h4>
          <el-input
            v-model="testResult"
            type="textarea"
            :rows="15"
            readonly
            style="font-family: monospace;"
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, RefreshLeft, Refresh, Plus, Delete, Edit, Connection, VideoPlay, VideoPause } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import api from './api'

// 威胁情报接口列表
const threatIntelligenceInterfaces = ref([])

const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const multipleSelection = ref([])

// 筛选表单
const filterForm = ref({
  name: '',
  type: '',
  status: ''
})

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref()
const submitting = ref(false)
const form = ref({
  name: '',
  type: '',
  description: '',
  config: {},
  enabled: true
})

// 测试相关
const testDialogVisible = ref(false)
const currentTestInterface = ref(null)
const testLoading = ref(false)
const testResult = ref('')
const testForm = ref({
  ioc: '',
  ioc_type: 'ip'
})

// 表单验证规则
const formRules = computed(() => {
  const baseRules = {
    name: [
      { required: true, message: '请输入接口名称', trigger: 'blur' }
    ],
    type: [
      { required: true, message: '请选择接口类型', trigger: 'change' }
    ]
  }

  if (form.value.type === 'tjun') {
    return {
      ...baseRules,
      'config.host': [
        { required: true, message: '请输入API域名', trigger: 'blur' }
      ],
      'config.app_key': [
        { required: true, message: '请输入APP KEY', trigger: 'blur' }
      ],
      'config.app_secret': [
        { required: true, message: '请输入APP密钥', trigger: 'blur' }
      ],
      'config.token': [
        { required: true, message: '请输入用户Token', trigger: 'blur' }
      ]
    }
  } else if (form.value.type === 'weibu') {
    return {
      ...baseRules,
      'config.host': [
        { required: true, message: '请输入API域名', trigger: 'blur' }
      ],
      'config.api_key': [
        { required: true, message: '请输入API密钥', trigger: 'blur' }
      ]
    }
  }

  return baseRules
})

// 获取接口类型标签类型
const getTypeTagType = (type) => {
  switch (type) {
    case 'tjun': return 'primary'
    case 'weibu': return 'success'
    default: return ''
  }
}

// 获取接口类型文本
const getTypeText = (type) => {
  switch (type) {
    case 'tjun': return '天际友盟'
    case 'weibu': return '微步威胁情报'
    default: return type
  }
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '-'
  return dayjs.unix(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

// 重置表单
const resetForm = () => {
  form.value = {
    name: '',
    type: '',
    description: '',
    config: {},
    enabled: true
  }
}

// 加载威胁情报接口列表
const loadThreatIntelligenceInterfaces = async () => {
  loading.value = true
  try {
    const response = await api.getThreatIntelligenceInterfaces({
      page: currentPage.value,
      page_size: pageSize.value,
      ...filterForm.value
    })

    threatIntelligenceInterfaces.value = response.data.data.list
    total.value = response.data.data.total
  } catch (error) {
    console.error('加载威胁情报接口列表失败:', error)
    ElMessage.error('加载威胁情报接口列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadThreatIntelligenceInterfaces()
}

const resetFilter = () => {
  filterForm.value = {
    name: '',
    type: '',
    status: ''
  }
  handleSearch()
}

const refreshList = () => {
  loadThreatIntelligenceInterfaces()
}

const handleSelectionChange = (selection) => {
  multipleSelection.value = selection
}

const handleSizeChange = (size) => {
  pageSize.value = size
  loadThreatIntelligenceInterfaces()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadThreatIntelligenceInterfaces()
}

const handleCreate = () => {
  dialogTitle.value = '新建威胁情报接口'
  resetForm()
  dialogVisible.value = true
}

const handleEdit = (row) => {
  dialogTitle.value = '编辑威胁情报接口'
  // 解析配置
  let config = {}
  if (row.config) {
    try {
      config = JSON.parse(row.config)
    } catch (e) {
      console.error('解析配置失败:', e)
    }
  }

  form.value = {
    id: row.id,
    name: row.name,
    type: row.type,
    description: row.description || '',
    config: config,
    enabled: row.status === 'enabled'
  }
  dialogVisible.value = true
}

const handleTypeChange = (type) => {
  if (type === 'tjun') {
    form.value.config = {
      host: 'api.tj-un.com',
      app_key: '',
      app_secret: '',
      token: '',
      timeout: 10
    }
  } else if (type === 'weibu') {
    form.value.config = {
      host: 'api.threatbook.cn',
      api_key: '',
      timeout: 10
    }
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  formRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        const data = {
          name: form.value.name,
          type: form.value.type,
          description: form.value.description,
          config: JSON.stringify(form.value.config),
          status: form.value.enabled ? 'enabled' : 'disabled'
        }

        if (form.value.id) {
          await api.updateThreatIntelligenceInterface(form.value.id, data)
          ElMessage.success('威胁情报接口更新成功')
        } else {
          await api.createThreatIntelligenceInterface(data)
          ElMessage.success('威胁情报接口创建成功')
        }

        dialogVisible.value = false
        loadThreatIntelligenceInterfaces()
      } catch (error) {
        console.error('保存威胁情报接口失败:', error)
        ElMessage.error('保存威胁情报接口失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除威胁情报接口 "${row.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await api.deleteThreatIntelligenceInterface(row.id)
    ElMessage.success('删除成功')
    loadThreatIntelligenceInterfaces()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除威胁情报接口失败:', error)
      ElMessage.error('删除威胁情报接口失败')
    }
  }
}

const handleBatchDelete = async () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请选择要删除的威胁情报接口')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${multipleSelection.value.length} 个威胁情报接口吗？`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const ids = multipleSelection.value.map(item => item.id)
    await api.batchDeleteThreatIntelligenceInterfaces(ids)
    ElMessage.success('批量删除成功')
    loadThreatIntelligenceInterfaces()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除威胁情报接口失败:', error)
      ElMessage.error('批量删除威胁情报接口失败')
    }
  }
}

const handleToggleStatus = async (row) => {
  try {
    const newStatus = row.status === 'enabled' ? 'disabled' : 'enabled'
    await api.updateThreatIntelligenceInterface(row.id, { status: newStatus })
    ElMessage.success(`威胁情报接口已${newStatus === 'enabled' ? '启用' : '禁用'}`)
    loadThreatIntelligenceInterfaces()
  } catch (error) {
    console.error('更新威胁情报接口状态失败:', error)
    ElMessage.error('更新威胁情报接口状态失败')
  }
}

const handleTest = (row) => {
  currentTestInterface.value = row
  testForm.value = {
    ioc: '',
    ioc_type: 'ip'
  }
  testResult.value = ''
  testDialogVisible.value = true
}

const executeTest = async () => {
  if (!currentTestInterface.value || !testForm.value.ioc) {
    ElMessage.warning('请输入要测试的IOC值')
    return
  }

  testLoading.value = true
  try {
    const response = await api.testThreatIntelligenceInterface(currentTestInterface.value.id, {
      ioc: testForm.value.ioc,
      ioc_type: testForm.value.ioc_type
    })

    testResult.value = JSON.stringify(response.data, null, 2)
    ElMessage.success('测试完成')
  } catch (error) {
    console.error('测试威胁情报接口失败:', error)
    testResult.value = `测试失败: ${error.response?.data?.msg || error.message}`
    ElMessage.error('测试威胁情报接口失败')
  } finally {
    testLoading.value = false
  }
}

onMounted(() => {
  loadThreatIntelligenceInterfaces()
})
</script>

<style scoped>
.threat-intelligence-interface-container {
  padding: 20px;
}

.filter-container {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-form {
  margin: 0;
}

.action-container {
  background: #fff;
  padding: 15px 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 10px;
}

.pagination-container {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  margin-top: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: flex-end;
}

.test-container {
  padding: 10px 0;
}

.test-info {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.test-info p {
  margin: 5px 0;
  color: #606266;
}

.test-actions {
  margin: 20px 0;
  text-align: center;
}

.test-result {
  margin-top: 20px;
}

.test-result h4 {
  margin-bottom: 10px;
  color: #303133;
}

/* 表格样式优化 */
.el-table {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 对话框样式优化 */
.el-dialog__body {
  padding: 20px;
}

/* 表单样式优化 */
.el-form-item {
  margin-bottom: 20px;
}

/* 按钮组样式 */
.el-button + .el-button {
  margin-left: 8px;
}

/* 标签样式 */
.el-tag {
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .threat-intelligence-interface-container {
    padding: 10px;
  }

  .filter-form .el-form-item {
    margin-bottom: 15px;
  }

  .action-container {
    flex-direction: column;
    gap: 8px;
  }

  .action-container .el-button {
    width: 100%;
  }
}
</style>
