package service

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
	"vulnerability_push/internal/models"
	"vulnerability_push/internal/utils"
)

// =============================================================================
// 天际友盟IOC客户端相关结构体和常量
// =============================================================================

// TJUNClient 天际友盟IOC请求客户端
type TJUNClient struct {
	host      string // API域名，如 api.tj-un.com
	appKey    string // APP KEY
	appSecret string // APP密钥
	timeout   time.Duration
	client    *http.Client
}

// RequestIOCV2Request /v2/requestIOC接口请求结构
type RequestIOCV2Request struct {
	Token string `json:"token"` // 用户身份标识，必填
	Type  string `json:"type"`  // 待查询数据的类型，必填，可选值为ip、domain、email、url、hash
	Value string `json:"value"` // 待查询数据值，必填
}

// RequestIOCV2Response /v2/requestIOC接口响应结构
type RequestIOCV2Response struct {
	ResponseStatus struct {
		Code    int    `json:"code"`    // 响应状态码：1成功，其他为错误
		Message string `json:"message"` // 响应消息
	} `json:"response_status"`
	ResponseData interface{} `json:"response_data"` // 响应数据，具体结构根据查询类型而定
}

// 常量定义
const (
	HMAC_SHA256                    = "HmacSHA256"
	ENCODING                       = "UTF-8"
	USER_AGENT                     = "demo/golang/tjun"
	LF                             = "\n"
	DEFAULT_TIMEOUT                = 5000 // 毫秒
	CA_HEADER_TO_SIGN_PREFIX_SYSTEM = "X-Ca-"

	// HTTP头常量
	HTTP_HEADER_ACCEPT       = "Accept"
	HTTP_HEADER_CONTENT_TYPE = "Content-Type"
	HTTP_HEADER_CONTENT_MD5  = "Content-MD5"
	HTTP_HEADER_DATE         = "Date"
	HTTP_HEADER_USER_AGENT   = "User-Agent"

	// 系统头常量
	X_CA_TIMESTAMP         = "X-Ca-Timestamp"
	X_CA_NONCE            = "X-Ca-Nonce"
	X_CA_KEY              = "X-Ca-Key"
	X_CA_SIGNATURE        = "X-Ca-Signature"
	X_CA_SIGNATURE_HEADERS = "X-Ca-Signature-Headers"

	// 内容类型常量
	CONTENT_TYPE_FORM = "application/x-www-form-urlencoded; charset=UTF-8"
	CONTENT_TYPE_JSON = "application/json; charset=UTF-8"
)

// TJUNIntelligenceService 天际友盟威胁情报服务
type TJUNIntelligenceService struct {
	db     *gorm.DB
	client *TJUNClient
	config TJUNConfig
}

// TJUNConfig 天际友盟配置
type TJUNConfig struct {
	Host      string
	AppKey    string
	AppSecret string
	Token     string
	Timeout   time.Duration
}

// TJUNQueryRequest 天际友盟查询请求
type TJUNQueryRequest struct {
	IOC     string `json:"ioc" binding:"required"`     // IOC值
	IOCType string `json:"iocType" binding:"required"` // IOC类型：ip, domain等
	IOCId   uint   `json:"iocId,omitempty"`            // IOC情报ID（可选，用于更新数据库记录）
}

// TJUNQueryResponse 天际友盟查询响应
type TJUNQueryResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// =============================================================================
// 天际友盟客户端工具函数
// =============================================================================

// generateUUID 生成UUID (简化版本)
func generateUUID() string {
	return fmt.Sprintf("%d-%d", time.Now().UnixNano(), time.Now().Unix())
}

// utf8ToISO88591 UTF-8转ISO-8859-1编码 (简化处理)
func utf8ToISO88591(s string) string {
	return s
}

// hmacSHA256 计算HMAC-SHA256签名
func hmacSHA256(data, secret string) string {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(data))
	return base64.StdEncoding.EncodeToString(h.Sum(nil))
}

// NewTJUNClient 创建新的天际友盟IOC客户端
func NewTJUNClient(host, appKey, appSecret string) *TJUNClient {
	return &TJUNClient{
		host:      host,
		appKey:    appKey,
		appSecret: appSecret,
		timeout:   5 * time.Second, // 默认5秒超时
		client: &http.Client{
			Timeout: 5 * time.Second,
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{
					InsecureSkipVerify: true, // 跳过SSL证书验证
				},
			},
		},
	}
}

// SetTimeout 设置请求超时时间
func (c *TJUNClient) SetTimeout(timeout time.Duration) *TJUNClient {
	c.timeout = timeout
	c.client.Timeout = timeout
	return c
}

// buildStringToSign 构建待签名字符串
func buildStringToSign(headers map[string]string, urlPath string, formParams map[string]string, method string) string {
	var sb strings.Builder

	// 1. HTTP方法
	sb.WriteString(strings.ToUpper(method))
	sb.WriteString(LF)

	// 2. Accept头
	if accept, ok := headers[HTTP_HEADER_ACCEPT]; ok {
		sb.WriteString(accept)
	}
	sb.WriteString(LF)

	// 3. Content-MD5头
	if contentMD5, ok := headers[HTTP_HEADER_CONTENT_MD5]; ok {
		sb.WriteString(contentMD5)
	}
	sb.WriteString(LF)

	// 4. Content-Type头
	if contentType, ok := headers[HTTP_HEADER_CONTENT_TYPE]; ok {
		sb.WriteString(contentType)
	}
	sb.WriteString(LF)

	// 5. Date头
	if date, ok := headers[HTTP_HEADER_DATE]; ok {
		sb.WriteString(date)
	}
	sb.WriteString(LF)

	// 6. 构建签名头
	sb.WriteString(buildHeaders(headers))

	// 7. 构建资源路径
	sb.WriteString(buildResource(urlPath, formParams))

	return sb.String()
}

// buildHeaders 构建待签名Http头
func buildHeaders(headers map[string]string) string {
	headersToSign := make(map[string]string)
	var signHeadersList []string

	// 收集需要签名的头
	for key, value := range headers {
		if isHeaderToSign(key) {
			headersToSign[key] = value
			signHeadersList = append(signHeadersList, key)
		}
	}

	// 按字典序排序
	sort.Strings(signHeadersList)

	// 设置签名头列表
	if len(signHeadersList) > 0 {
		headers[X_CA_SIGNATURE_HEADERS] = strings.Join(signHeadersList, ",")
	}

	// 构建签名字符串
	var sb strings.Builder
	for _, key := range signHeadersList {
		sb.WriteString(key)
		sb.WriteString(":")
		sb.WriteString(headersToSign[key])
		sb.WriteString(LF)
	}

	return sb.String()
}

// buildResource 构建待签名Path+Query+FormParams
func buildResource(urlPath string, formParams map[string]string) string {
	var sb strings.Builder

	// 解析URL，分离路径和查询参数
	parsedURL, err := url.Parse(urlPath)
	if err != nil {
		sb.WriteString(urlPath)
	} else {
		sb.WriteString(parsedURL.Path)

		// 合并查询参数和表单参数
		allParams := make(map[string]string)

		// 添加URL查询参数
		for key, values := range parsedURL.Query() {
			if len(values) > 0 {
				allParams[key] = values[0]
			} else {
				allParams[key] = ""
			}
		}

		// 添加表单参数
		for key, value := range formParams {
			if _, exists := allParams[key]; !exists {
				allParams[key] = value
			}
		}

		// 按字典序排序参数
		if len(allParams) > 0 {
			sb.WriteString("?")

			var keys []string
			for key := range allParams {
				keys = append(keys, key)
			}
			sort.Strings(keys)

			for i, key := range keys {
				if i > 0 {
					sb.WriteString("&")
				}

				value := allParams[key]
				if value == "" {
					sb.WriteString(key)
				} else {
					sb.WriteString(key)
					sb.WriteString("=")
					sb.WriteString(value)
				}
			}
		}
	}

	return sb.String()
}

// isHeaderToSign 判断Http头是否参与签名
func isHeaderToSign(headerName string) bool {
	if headerName == "" {
		return false
	}

	// 系统头参与签名
	if strings.HasPrefix(headerName, CA_HEADER_TO_SIGN_PREFIX_SYSTEM) {
		return true
	}

	return false
}

// initialBasicHeader 初始化基础Header
func (c *TJUNClient) initialBasicHeader(headers map[string]string, method, requestURL string, formParams map[string]string) map[string]string {
	if headers == nil {
		headers = make(map[string]string)
	}

	// 解析URL获取路径和查询参数
	parsedURL, err := url.Parse(requestURL)
	if err != nil {
		return headers
	}

	var pathAndQuery strings.Builder
	if parsedURL.Path != "" {
		pathAndQuery.WriteString(parsedURL.Path)
	}
	if parsedURL.RawQuery != "" {
		pathAndQuery.WriteString("?")
		pathAndQuery.WriteString(parsedURL.RawQuery)
	}

	// 设置基础头部
	headers[HTTP_HEADER_USER_AGENT] = USER_AGENT
	headers[X_CA_TIMESTAMP] = strconv.FormatInt(time.Now().UnixMilli(), 10)
	headers[X_CA_NONCE] = generateUUID()
	headers[X_CA_KEY] = c.appKey

	// 计算签名 (必须在设置完所有头部后计算)
	signature := hmacSHA256(buildStringToSign(headers, pathAndQuery.String(), formParams, method), c.appSecret)
	headers[X_CA_SIGNATURE] = signature

	return headers
}

// RequestIOCV2 调用/v2/requestIOC接口
func (c *TJUNClient) RequestIOCV2(ctx context.Context, token, iocType, value string) (*RequestIOCV2Response, error) {
	// 参数验证
	if c.appKey == "" {
		return nil, fmt.Errorf("请填写APP KEY")
	}
	if c.appSecret == "" {
		return nil, fmt.Errorf("请填写APP SECRET")
	}
	if c.host == "" {
		return nil, fmt.Errorf("请填写host")
	}
	if token == "" {
		return nil, fmt.Errorf("请填写token")
	}
	if iocType == "" {
		return nil, fmt.Errorf("请填写type")
	}
	if value == "" {
		return nil, fmt.Errorf("请填写value")
	}

	// 构建请求URL
	requestURL := fmt.Sprintf("https://%s/v2/requestIOC", c.host)

	// 构建表单参数
	formParams := map[string]string{
		"token": token,
		"type":  iocType,
		"value": value,
	}

	// 初始化请求头
	headers := map[string]string{
		HTTP_HEADER_ACCEPT:       "application/json",
		HTTP_HEADER_CONTENT_TYPE: CONTENT_TYPE_FORM,
	}

	// 初始化基础头部和签名
	headers = c.initialBasicHeader(headers, "POST", requestURL, formParams)

	return c.executeRequest(ctx, "POST", requestURL, headers, formParams)
}

// RequestIOCReputation 调用/v1/reputation接口
func (c *TJUNClient) RequestIOCReputation(ctx context.Context, token, iocType, value, struct_, nextpage string) (*RequestIOCV2Response, error) {
	// 参数验证
	if c.appKey == "" {
		return nil, fmt.Errorf("请填写APP KEY")
	}
	if c.appSecret == "" {
		return nil, fmt.Errorf("请填写APP SECRET")
	}
	if c.host == "" {
		return nil, fmt.Errorf("请填写host")
	}
	if token == "" {
		return nil, fmt.Errorf("请填写token")
	}
	if iocType == "" {
		return nil, fmt.Errorf("请填写type")
	}
	if value == "" {
		return nil, fmt.Errorf("请填写value")
	}
	if struct_ == "" {
		return nil, fmt.Errorf("请填写struct")
	}

	// 构建请求URL
	requestURL := fmt.Sprintf("https://%s/v1/reputation", c.host)

	// 构建表单参数
	formParams := map[string]string{
		"token":  token,
		"type":   iocType,
		"value":  value,
		"struct": struct_,
	}

	// 添加可选的nextpage参数
	if nextpage != "" {
		formParams["nextpage"] = nextpage
	}

	// 初始化请求头
	headers := map[string]string{
		HTTP_HEADER_ACCEPT:       "application/json",
		HTTP_HEADER_CONTENT_TYPE: CONTENT_TYPE_FORM,
	}

	// 初始化基础头部和签名
	headers = c.initialBasicHeader(headers, "POST", requestURL, formParams)

	return c.executeRequest(ctx, "POST", requestURL, headers, formParams)
}

// executeRequest 执行HTTP请求
func (c *TJUNClient) executeRequest(ctx context.Context, method, requestURL string, headers map[string]string, formParams map[string]string) (*RequestIOCV2Response, error) {
	// 构建表单数据 (application/x-www-form-urlencoded格式)
	formData := url.Values{}
	for key, value := range formParams {
		formData.Set(key, value)
	}

	// 创建请求
	var req *http.Request
	var err error

	if method == "POST" {
		// POST请求，使用表单数据作为请求体
		req, err = http.NewRequestWithContext(ctx, method, requestURL, strings.NewReader(formData.Encode()))
	} else {
		req, err = http.NewRequestWithContext(ctx, method, requestURL, nil)
	}

	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置请求头
	for key, value := range headers {
		req.Header.Set(key, utf8ToISO88591(value))
	}

	// 调试输出请求信息
	utils.Infof("请求URL: %s\n", requestURL)
	utils.Infof("请求方法: %s\n", method)
	utils.Infof("请求头:\n")
	for key, value := range headers {
		utils.Infof("  %s: %s\n", key, value)
	}
	utils.Infof("请求体: %s\n", formData.Encode())

	// 执行请求，支持重试
	var resp *http.Response
	for i := 0; i < 3; i++ {
		resp, err = c.client.Do(req)
		if err != nil {
			if i == 2 { // 最后一次重试
				return nil, fmt.Errorf("发送HTTP请求失败: %v", err)
			}
			continue
		}

		// 检查状态码
		if resp.StatusCode == 200 {
			break
		} else if resp.StatusCode >= 500 {
			resp.Body.Close()
			if i == 2 { // 最后一次重试
				return nil, fmt.Errorf("服务器错误，状态码: %d", resp.StatusCode)
			}
			continue
		} else {
			// 客户端错误，不重试
			break
		}
	}

	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应体失败: %v", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("API返回错误状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析响应数据
	var response RequestIOCV2Response
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析响应数据失败: %v, 响应内容: %s", err, string(body))
	}

	// 检查业务状态码 (天际友盟API成功状态码为1)
	if response.ResponseStatus.Code != 1 {
		return nil, fmt.Errorf("API返回业务错误: code=%d, message=%s", response.ResponseStatus.Code, response.ResponseStatus.Message)
	}

	return &response, nil
}

// NewTJUNIntelligenceService 创建天际友盟威胁情报服务
func NewTJUNIntelligenceService(db *gorm.DB, config TJUNConfig) *TJUNIntelligenceService {
	client := NewTJUNClient(config.Host, config.AppKey, config.AppSecret)
	if config.Timeout > 0 {
		client.SetTimeout(config.Timeout)
	} else {
		client.SetTimeout(10 * time.Second) // 默认超时时间
	}

	return &TJUNIntelligenceService{
		db:     db,
		client: client,
		config: config,
	}
}

// QueryIOC 查询单个IOC的威胁情报
func (s *TJUNIntelligenceService) QueryIOC(req TJUNQueryRequest) (*TJUNQueryResponse, error) {
	// 创建上下文
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()

	// 根据IOC类型调用不同的接口
	var response interface{}

	if strings.ToLower(req.IOCType) == "ip" {
		// 对于IP类型，调用reputation接口
		ipArray := fmt.Sprintf(`["%s"]`, req.IOC)
		reputationResp, queryErr := s.client.RequestIOCReputation(ctx, s.config.Token, "ip", ipArray, "ip_reputation", "")
		if queryErr != nil {
			return &TJUNQueryResponse{
				Success: false,
				Message: "查询天际友盟IOC失败: " + queryErr.Error(),
			}, queryErr
		}
		response = reputationResp.ResponseData
	} else {
		// 对于其他类型，调用requestIOC接口
		iocResp, queryErr := s.client.RequestIOCV2(ctx, s.config.Token, req.IOCType, req.IOC)
		if queryErr != nil {
			return &TJUNQueryResponse{
				Success: false,
				Message: "查询天际友盟IOC失败: " + queryErr.Error(),
			}, queryErr
		}
		response = iocResp.ResponseData
	}

	// 如果提供了IOC ID，更新数据库记录
	if req.IOCId > 0 {
		if updateErr := s.updateIOCRecord(req.IOCId, response, ""); updateErr != nil {
			// 记录错误但不影响返回结果
			fmt.Printf("更新IOC情报天际友盟数据失败: %v\n", updateErr)
		}
	}

	return &TJUNQueryResponse{
		Success: true,
		Message: "查询成功",
		Data:    response,
	}, nil
}

// updateIOCRecord 更新IOC记录的天际友盟数据
func (s *TJUNIntelligenceService) updateIOCRecord(iocId uint, data interface{}, errorMessage string) error {
	updateData := map[string]interface{}{
		"tjun_query_time": time.Now().Unix(),
	}

	if errorMessage != "" {
		updateData["tjun_query_status"] = "error"
		updateData["tjun_error_message"] = errorMessage
	} else {
		updateData["tjun_query_status"] = "success"
		updateData["tjun_error_message"] = ""

		if serializedData, err := json.Marshal(data); err == nil {
			updateData["tjun_data"] = string(serializedData)
		} else {
			updateData["tjun_error_message"] = fmt.Sprintf("序列化天际友盟数据失败: %v", err)
		}
	}

	return s.db.Model(&models.IOCIntelligence{}).Where("id = ?", iocId).Updates(updateData).Error
}

// BatchQueryIOCs 批量查询IOC威胁情报
func (s *TJUNIntelligenceService) BatchQueryIOCs(iocs []string, iocType string) (map[string]interface{}, error) {
	if len(iocs) == 0 {
		return nil, fmt.Errorf("IOC列表为空")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	results := make(map[string]interface{})

	if strings.ToLower(iocType) == "ip" {
		// 对于IP类型，使用批量reputation接口
		ipArray, err := json.Marshal(iocs)
		if err != nil {
			return nil, fmt.Errorf("序列化IP列表失败: %v", err)
		}

		reputationResp, err := s.client.RequestIOCReputation(ctx, s.config.Token, "ip", string(ipArray), "ip_reputation", "")
		if err != nil {
			return nil, fmt.Errorf("批量查询天际友盟IP reputation失败: %v", err)
		}

		results["batch_result"] = reputationResp.ResponseData
	} else {
		// 对于其他类型，逐个查询
		for _, ioc := range iocs {
			iocResp, err := s.client.RequestIOCV2(ctx, s.config.Token, iocType, ioc)
			if err != nil {
				results[ioc] = map[string]interface{}{
					"error": err.Error(),
				}
			} else {
				results[ioc] = iocResp.ResponseData
			}
		}
	}

	return results, nil
}

// BatchUpdateUnqueriedIOCs 批量更新未查询的IOC记录
func (s *TJUNIntelligenceService) BatchUpdateUnqueriedIOCs() (int, error) {
	// 获取所有未查询天际友盟情报的IOC记录
	var iocList []models.IOCIntelligence
	if err := s.db.Where("tjun_query_status = ? OR tjun_query_status IS NULL", "not_queried").Find(&iocList).Error; err != nil {
		return 0, fmt.Errorf("获取IOC情报列表失败: %v", err)
	}

	if len(iocList) == 0 {
		return 0, nil
	}

	successCount := 0
	for _, ioc := range iocList {
		req := TJUNQueryRequest{
			IOC:     ioc.IOC,
			IOCType: ioc.IOCType,
			IOCId:   ioc.ID,
		}

		_, err := s.QueryIOC(req)
		if err != nil {
			// 更新错误状态
			s.updateIOCRecord(ioc.ID, nil, err.Error())
			fmt.Printf("查询IOC %s 失败: %v\n", ioc.IOC, err)
		} else {
			successCount++
		}

		// 添加延迟避免请求过于频繁
		time.Sleep(100 * time.Millisecond)
	}

	return successCount, nil
}

// GetDefaultConfig 获取默认配置
func GetDefaultTJUNConfig() TJUNConfig {
	return TJUNConfig{
		Host:      "api.tj-un.com",
		AppKey:    "24634572",
		AppSecret: "812d078fe8143e529599f9d6689a6046",
		Token:     "9fd3b7f517dc4674b7f001e0b1ed7c61",
		Timeout:   10 * time.Second,
	}
}
