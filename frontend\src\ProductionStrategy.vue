<template>
  <div class="production-strategy-container">
    <el-card shadow="hover" class="config-card">
      <div class="card-header">
        <div class="card-title">
          <el-icon><Setting /></el-icon>
          生产策略配置
        </div>
        <div class="card-actions">
          <el-button type="primary" @click="handleSave" :loading="saving">
            <el-icon><Check /></el-icon> 保存配置
          </el-button>
          <el-button @click="handleReset">
            <el-icon><RefreshLeft /></el-icon> 重置
          </el-button>
          <el-button
            type="success"
            @click="handleExecute"
            :disabled="!config.id"
          >
            <el-icon><Connection /></el-icon> 立即执行
          </el-button>
        </div>
      </div>

      <el-form
        ref="formRef"
        :model="config"
        :rules="rules"
        label-width="140px"
        v-loading="loading"
      >
        <!-- 攻击过滤配置 -->
        <el-divider content-position="left">攻击过滤配置</el-divider>

        <el-form-item label="攻击次数阈值" prop="attackCountThreshold">
          <el-input-number
            v-model="config.attackCountThreshold"
            :min="1"
            :max="1000"
            placeholder="设置最小攻击次数"
            style="width: 300px"
          />
          <div class="form-help-text">只有攻击次数达到此阈值的记录才会被生成为IOC情报</div>
        </el-form-item>

        <el-form-item label="威胁评分阈值" prop="threatScoreThreshold">
          <el-input-number
            v-model="config.threatScoreThreshold"
            :min="0"
            :max="10"
            placeholder="设置最小威胁评分"
            style="width: 300px"
          />
          <div class="form-help-text">只有威胁评分达到此阈值的记录才会被生成为IOC情报</div>
        </el-form-item>

        <el-form-item label="启用威胁评分">
          <el-switch v-model="config.enableThreatScoring" />
          <div class="form-help-text">是否启用威胁评分计算和过滤</div>
        </el-form-item>

        <!-- 有效期配置 -->
        <el-divider content-position="left">有效期配置</el-divider>

        <el-form-item label="启用有效期控制">
          <el-switch v-model="config.enableValidityControl" />
          <div class="form-help-text">是否启用IOC情报有效期控制</div>
        </el-form-item>

        <el-form-item label="默认有效期(天)" prop="defaultValidityDays" v-if="config.enableValidityControl">
          <el-input-number
            v-model="config.defaultValidityDays"
            :min="1"
            :max="100"
            placeholder="设置默认有效期天数"
            style="width: 300px"
          />
          <div class="form-help-text">新生成IOC情报的默认有效期，单位：天（1-100天）</div>
        </el-form-item>

        <!-- 威胁情报查询配置 -->
        <el-divider content-position="left">威胁情报查询配置</el-divider>

        <el-form-item label="启用天际友盟查询">
          <el-switch v-model="config.enableTJUNQuery" />
          <div class="form-help-text">是否在生产IOC情报时自动查询天际友盟威胁情报</div>
        </el-form-item>

        <el-form-item label="启用微步查询">
          <el-switch v-model="config.enableWeibuQuery" />
          <div class="form-help-text">是否在生产IOC情报时自动查询微步威胁情报（仅支持IP类型）</div>
        </el-form-item>

        <!-- 定时任务配置 -->
        <el-divider content-position="left">定时任务配置</el-divider>

        <el-form-item label="启用定时任务">
          <el-switch v-model="config.scheduleEnabled" />
          <div class="form-help-text">是否启用定时任务自动生成IOC情报</div>
        </el-form-item>

        <el-form-item label="任务间隔(分钟)" prop="scheduleInterval" v-if="config.scheduleEnabled">
          <el-input-number
            v-model="config.scheduleInterval"
            :min="1"
            :max="1440"
            placeholder="设置定时任务间隔"
            style="width: 300px"
          />
          <div class="form-help-text">定时任务执行间隔，单位：分钟（1-1440分钟）</div>
        </el-form-item>

        <el-form-item label="上次运行时间" v-if="config.lastRunTime">
          <el-input
            :value="formatTimestamp(config.lastRunTime)"
            readonly
            style="width: 300px"
          />
        </el-form-item>

        <el-form-item label="下次运行时间" v-if="config.nextRunTime">
          <el-input
            :value="formatTimestamp(config.nextRunTime)"
            readonly
            style="width: 300px"
          />
        </el-form-item>

        <!-- 时间配置 -->
        <el-divider content-position="left">时间配置</el-divider>

        <el-form-item label="时间范围(小时)" prop="timeRangeHours">
          <el-input-number
            v-model="config.timeRangeHours"
            :min="1"
            :max="8760"
            placeholder="设置时间范围"
            style="width: 300px"
          />
          <div class="form-help-text">处理多长时间范围内的攻击数据（小时）</div>
        </el-form-item>

        <!-- 执行统计信息 -->
        <el-divider content-position="left">执行统计</el-divider>

        <el-form-item label="策略状态">
          <el-tag :type="config.status === 'enabled' ? 'success' : 'info'">
            {{ config.status === 'enabled' ? '已启用' : '已禁用' }}
          </el-tag>
        </el-form-item>

        <el-form-item label="创建时间">
          <span v-if="config.createdAt" class="info-text">
            {{ formatDate(config.createdAt * 1000) }}
          </span>
          <span v-else class="text-gray-400">-</span>
        </el-form-item>

        <el-form-item label="更新时间">
          <span v-if="config.updatedAt" class="info-text">
            {{ formatDate(config.updatedAt * 1000) }}
          </span>
          <span v-else class="text-gray-400">-</span>
        </el-form-item>

        <el-form-item label="执行日志">
          <el-button
            type="info"
            @click="handleViewLogs"
            :disabled="!config.id"
          >
            <el-icon><Document /></el-icon> 查看日志
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 日志查看对话框 -->
    <el-dialog
      v-model="logDialogVisible"
      title="执行日志"
      width="1000px"
      @close="resetLogDialog"
    >
      <div class="log-dialog-content">
        <!-- 日志筛选 -->
        <div class="log-filter-container">
          <el-form :inline="true" :model="logFilterForm" class="log-filter-form">
            <el-form-item label="状态">
              <el-select
                v-model="logFilterForm.status"
                placeholder="全部状态"
                clearable
                style="width: 120px;"
                @change="loadLogs"
              >
                <el-option label="成功" value="success" />
                <el-option label="失败" value="failed" />
                <el-option label="运行中" value="running" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="loadLogs" :icon="Refresh">
                刷新
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 日志列表 -->
        <el-table
          :data="logs"
          v-loading="logLoading"
          stripe
          style="width: 100%"
          max-height="400px"
        >
          <el-table-column prop="status" label="状态" width="80">
            <template #default="scope">
              <el-tag :type="getLogStatusTagType(scope.row.status)" size="small">
                {{ getLogStatusLabel(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="processedAt" label="执行时间" width="160">
            <template #default="scope">
              {{ formatDate(scope.row.processedAt * 1000) }}
            </template>
          </el-table-column>
          <el-table-column prop="message" label="执行消息" min-width="300" show-overflow-tooltip />
          <el-table-column label="操作" width="100">
            <template #default="scope">
              <el-button
                v-if="scope.row.status === 'failed'"
                type="danger"
                size="small"
                @click="showErrorDetails(scope.row)"
              >
                错误详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 日志分页 -->
        <div class="log-pagination-container">
          <el-pagination
            v-model:current-page="logCurrentPage"
            v-model:page-size="logPageSize"
            :page-sizes="[10, 20, 50]"
            :total="logTotal"
            layout="total, sizes, prev, pager, next"
            @size-change="handleLogSizeChange"
            @current-change="handleLogCurrentChange"
          />
        </div>
      </div>
    </el-dialog>

    <!-- 错误详情对话框 -->
    <el-dialog
      v-model="errorDialogVisible"
      title="错误详情"
      width="600px"
    >
      <el-input
        v-model="errorDetails"
        type="textarea"
        :rows="10"
        readonly
        placeholder="无错误详情"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Setting, Check, RefreshLeft, Connection, Document, Refresh } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import api from './api'

// 数据
const loading = ref(false)
const saving = ref(false)
const formRef = ref()

// 日志相关数据
const logDialogVisible = ref(false)
const logLoading = ref(false)
const logs = ref([])
const logCurrentPage = ref(1)
const logPageSize = ref(20)
const logTotal = ref(0)

// 日志筛选表单
const logFilterForm = ref({
  status: ''
})

// 错误详情对话框
const errorDialogVisible = ref(false)
const errorDetails = ref('')

// 配置数据
const config = ref({
  id: null,
  name: '生产策略配置',
  description: 'IOC情报生产策略全局配置',
  status: 'enabled',
  attackCountThreshold: 1,
  threatScoreThreshold: 0,
  enableThreatScoring: true,
  riskLevelFilter: '',
  // 有效期配置
  defaultValidityDays: 30,
  enableValidityControl: true,
  // 威胁情报查询配置
  enableTJUNQuery: true,
  enableWeibuQuery: true,
  // 定时任务配置
  scheduleEnabled: false,
  scheduleInterval: 60,
  lastRunTime: null,
  nextRunTime: null,
  timeRangeHours: 24,
  allowedCountries: '',
  blockedCountries: '',
  allowedIPRanges: '',
  blockedIPRanges: '',
  createdBy: '',
  createdAt: null,
  updatedAt: null
})

// 表单验证规则
const rules = {
  attackCountThreshold: [
    { required: true, message: '请输入攻击次数阈值', trigger: 'blur' },
    { type: 'number', min: 1, max: 1000, message: '攻击次数阈值必须在1-1000之间', trigger: 'blur' }
  ],
  threatScoreThreshold: [
    { required: true, message: '请输入威胁评分阈值', trigger: 'blur' },
    { type: 'number', min: 0, max: 10, message: '威胁评分阈值必须在0-10之间', trigger: 'blur' }
  ],
  defaultValidityDays: [
    { required: true, message: '请输入默认有效期天数', trigger: 'blur' },
    { type: 'number', min: 1, max: 100, message: '默认有效期天数必须在1-100天之间', trigger: 'blur' }
  ],
  scheduleInterval: [
    { required: true, message: '请输入定时任务间隔', trigger: 'blur' },
    { type: 'number', min: 1, max: 1440, message: '定时任务间隔必须在1-1440分钟之间', trigger: 'blur' }
  ],
  timeRangeHours: [
    { required: true, message: '请输入时间范围', trigger: 'blur' },
    { type: 'number', min: 1, max: 8760, message: '时间范围必须在1-8760小时之间', trigger: 'blur' }
  ]
}

// 计算属性
const isAdmin = computed(() => {
  const userRole = localStorage.getItem('userRole')
  return userRole === 'admin'
})

// 方法
const formatDate = (dateString) => {
  if (!dateString) return '-'
  return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss')
}

const formatTimestamp = (timestamp) => {
  if (!timestamp) return '-'
  return dayjs(timestamp * 1000).format('YYYY-MM-DD HH:mm:ss')
}

const getRunStatusLabel = (status) => {
  const labels = {
    'success': '成功',
    'failed': '失败',
    'running': '运行中'
  }
  return labels[status] || status
}

const getRunStatusTagType = (status) => {
  const types = {
    'success': 'success',
    'failed': 'danger',
    'running': 'warning'
  }
  return types[status] || 'default'
}

// 日志状态相关方法
const getLogStatusLabel = (status) => {
  const labels = {
    'success': '成功',
    'failed': '失败',
    'running': '运行中'
  }
  return labels[status] || status
}

const getLogStatusTagType = (status) => {
  const types = {
    'success': 'success',
    'failed': 'danger',
    'running': 'warning'
  }
  return types[status] || 'default'
}

// 格式化执行时长
const formatDuration = (duration) => {
  if (!duration) return '-'

  if (duration < 1000) {
    return `${duration}ms`
  } else if (duration < 60000) {
    return `${(duration / 1000).toFixed(1)}s`
  } else {
    const minutes = Math.floor(duration / 60000)
    const seconds = Math.floor((duration % 60000) / 1000)
    return `${minutes}m ${seconds}s`
  }
}

// 加载配置数据
const loadConfig = async () => {
  loading.value = true
  try {
    const response = await api.getProductionStrategyConfig()

    if (response.data && response.data.data) {
      const strategy = response.data.data
      config.value = {
        ...strategy
      }
    }
  } catch (error) {
    console.error('加载生产策略配置失败:', error)
    ElMessage.error('加载生产策略配置失败')
  } finally {
    loading.value = false
  }
}



// 保存配置
const handleSave = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    saving.value = true

    // 准备提交数据
    const submitData = { ...config.value }

    if (config.value.id) {
      await api.updateProductionStrategy(config.value.id, submitData)
    } else {
      const response = await api.createProductionStrategy(submitData)
      if (response.data && response.data.data) {
        config.value.id = response.data.data
      }
    }

    ElMessage.success('配置保存成功')
    await loadConfig() // 重新加载配置以获取最新状态
  } catch (error) {
    console.error('保存配置失败:', error)
    ElMessage.error('保存配置失败')
  } finally {
    saving.value = false
  }
}

// 重置配置
const handleReset = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重置配置吗？这将恢复到默认设置。',
      '确认重置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await loadConfig()
    ElMessage.success('配置已重置')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重置配置失败:', error)
      ElMessage.error('重置配置失败')
    }
  }
}

// 执行策略
const handleExecute = async () => {
  if (!config.value.id) {
    ElMessage.warning('请先保存配置')
    return
  }

  try {
    await api.executeProductionStrategy(config.value.id)
    ElMessage.success('策略执行已启动')

    // 延迟刷新以获取最新执行状态
    setTimeout(() => {
      loadConfig()
    }, 2000)
  } catch (error) {
    console.error('执行策略失败:', error)
    ElMessage.error('执行策略失败')
  }
}

// 查看执行日志
const handleViewLogs = async () => {
  if (!config.value.id) {
    ElMessage.warning('请先保存配置')
    return
  }

  try {
    logDialogVisible.value = true
    logCurrentPage.value = 1
    logFilterForm.value.status = ''
    await loadLogs()
  } catch (error) {
    console.error('查看日志失败:', error)
    ElMessage.error('查看日志失败')
  }
}

// 加载执行日志
const loadLogs = async () => {
  if (!config.value.id) return

  logLoading.value = true
  try {
    const params = {
      page: logCurrentPage.value,
      page_size: logPageSize.value
    }

    // 如果有状态筛选，添加到参数中
    if (logFilterForm.value.status) {
      params.status = logFilterForm.value.status
    }

    const response = await api.getProductionStrategyLogs(config.value.id, params)
    logs.value = response.data.data?.list || []
    logTotal.value = response.data.data?.pagination?.total || 0
  } catch (error) {
    console.error('获取日志失败:', error)
    ElMessage.error('获取日志失败')
    logs.value = []
    logTotal.value = 0
  } finally {
    logLoading.value = false
  }
}

// 日志分页处理
const handleLogSizeChange = (val) => {
  logPageSize.value = val
  logCurrentPage.value = 1
  loadLogs()
}

const handleLogCurrentChange = (val) => {
  logCurrentPage.value = val
  loadLogs()
}

// 重置日志对话框
const resetLogDialog = () => {
  logs.value = []
  logTotal.value = 0
  logCurrentPage.value = 1
  logPageSize.value = 20
  logFilterForm.value.status = ''
}

// 显示错误详情
const showErrorDetails = (log) => {
  errorDetails.value = log.message || '无错误详情'
  errorDialogVisible.value = true
}

// 初始化
onMounted(() => {
  loadConfig()
})
</script>

<style scoped>
.production-strategy-container {
  padding: 20px;
}

.config-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.card-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-actions {
  display: flex;
  gap: 10px;
}

.form-help-text {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.text-gray-400 {
  color: #9ca3af;
}

.info-text {
  color: #606266;
  font-size: 14px;
}

.stats-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 14px;
  color: #606266;
}

.stats-info > div {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 表单样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-divider__text) {
  font-weight: 600;
  color: #409eff;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

/* 日志对话框样式 */
.log-dialog-content {
  max-height: 600px;
}

.log-filter-container {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 15px;
}

.log-filter-form {
  margin: 0;
}

.log-pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

.text-sm {
  font-size: 12px;
  line-height: 1.4;
}
</style>
