<template>
  <div class="app-container">
    <el-container v-if="isLoggedIn">
      <el-aside width="220px">
        <div class="logo">
          <h1>威胁情报管理平台</h1>
        </div>
        <el-menu
          :default-active="activeMenu"
          class="el-menu-vertical"
          router
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
        >
          <el-menu-item index="/home">
            <el-icon><HomeFilled /></el-icon>
            <span>首页</span>
          </el-menu-item>
          
          <el-sub-menu index="ioc-intelligence">
            <template #title>
              <el-icon><Monitor /></el-icon>
              <span>IOC情报</span>
            </template>
            <el-menu-item index="/ioc-intelligence">
              <el-icon><DataLine /></el-icon>
              <span>情报列表</span>
            </el-menu-item>
            <el-menu-item index="/ioc-intelligence-data">
              <el-icon><DataLine /></el-icon>
              <span>源数据列表</span>
            </el-menu-item>
            <el-menu-item index="/ioc-whitelist">
              <el-icon><List /></el-icon>
              <span>白名单</span>
            </el-menu-item>
            <el-menu-item index="/data-interface">
              <el-icon><Link /></el-icon>
              <span>数据接口</span>
            </el-menu-item>
            <el-menu-item index="/ip-collection">
              <el-icon><Setting /></el-icon>
              <span>生产策略</span>
            </el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="vulnerability-intelligence">
            <template #title>
              <el-icon><Warning /></el-icon>
              <span>漏洞情报</span>
            </template>
            <el-menu-item index="/vulnerabilities">
              <el-icon><DataLine /></el-icon>
              <span>漏洞列表</span>
            </el-menu-item>
            <el-menu-item index="/crawlers" v-if="isAdmin">
              <el-icon><Connection /></el-icon>
              <span>漏洞采集</span>
            </el-menu-item>
          </el-sub-menu>
          
          <el-menu-item index="/push" v-if="isAdmin">
            <el-icon><Promotion /></el-icon>
            <span>推送管理</span>
          </el-menu-item>
          
          <el-menu-item index="/users" v-if="isAdmin">
            <el-icon><User /></el-icon>
            <span>用户管理</span>
          </el-menu-item>
        </el-menu>
      </el-aside>
      
      <el-container>
        <el-header>
          <div class="header-content">
            <div class="header-title">
              <h2>{{ getPageTitle }}</h2>
            </div>
            <div class="user-info">
              <span v-if="currentUser">{{ currentUser.username }}</span>
              <el-dropdown trigger="click">
                <el-avatar :size="32" :style="avatarStyle">
                  {{ userInitial }}
                </el-avatar>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="showChangePasswordDialog">修改密码</el-dropdown-item>
                    <el-dropdown-item @click="logout">退出登录</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </el-header>
        
        <el-main>
          <router-view />
        </el-main>
      </el-container>
    </el-container>
    
    <!-- 未登录时只显示路由视图 -->
    <router-view v-else />
    
    <!-- 修改密码对话框 -->
    <el-dialog v-model="changePasswordDialogVisible" title="修改密码" width="500px" destroy-on-close>
      <el-form :model="passwordForm" :rules="passwordRules" ref="passwordFormRef" label-width="100px" status-icon>
        <el-form-item label="旧密码" prop="oldPassword">
          <el-input v-model="passwordForm.oldPassword" type="password" show-password />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="passwordForm.newPassword" type="password" show-password />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="passwordForm.confirmPassword" type="password" show-password />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="changePasswordDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleChangePassword" :loading="changingPassword">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { HomeFilled, User, Warning, Promotion, Connection, Monitor, Setting, List, Link, DataLine } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import api from './api'

const router = useRouter()
const route = useRoute()
const currentUser = ref(null)
const changePasswordDialogVisible = ref(false)
const changingPassword = ref(false)
const passwordFormRef = ref(null)

const passwordForm = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入旧密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (value !== passwordForm.value.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ]
}

// 添加一个响应式变量来跟踪登录状态
const loginState = ref(!!localStorage.getItem('token'))

// 更新isLoggedIn计算属性
const isLoggedIn = computed(() => {
  return loginState.value
})

// 监听存储变化，更新登录状态
const updateLoginState = () => {
  loginState.value = !!localStorage.getItem('token')
}

const isAdmin = computed(() => {
  return currentUser.value && currentUser.value.role === 'admin'
})

const activeMenu = computed(() => {
  return route.path
})

// 获取用户名首字母
const userInitial = computed(() => {
  if (!currentUser.value || !currentUser.value.username) return '?'
  return currentUser.value.username.charAt(0).toUpperCase()
})

// 根据用户名生成随机背景色
const avatarStyle = computed(() => {
  if (!currentUser.value || !currentUser.value.username) return {}
  
  // 使用用户名的首字母 ASCII 码来生成颜色
  const charCode = currentUser.value.username.charCodeAt(0)
  const hue = (charCode * 7) % 360
  
  return {
    backgroundColor: `hsl(${hue}, 70%, 50%)`,
    color: 'white',
    fontWeight: 'bold',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center'
  }
})

const getPageTitle = computed(() => {
  if (route.path === '/home') return '首页'
  if (route.path === '/users') return '用户管理'
  if (route.path === '/vulnerabilities') return '漏洞列表'
  if (route.path === '/push') return '推送管理'
  if (route.path === '/crawlers') return '漏洞采集'
  if (route.path === '/ioc-intelligence') return 'IOC情报列表'
  if (route.path === '/ioc-intelligence-data') return 'IOC源数据列表'
  if (route.path === '/ioc-whitelist') return 'IOC白名单'
  if (route.path === '/data-interface') return '数据接口'
  if (route.path === '/ip-collection') return '生产策略'
  return ''
})

const logout = () => {
  // 先清除token
  localStorage.removeItem('token')
  currentUser.value = null
  loginState.value = false
  // 然后再导航到登录页面，使用replace而不是push
  router.replace('/')
}

const fetchCurrentUser = async () => {
  if (isLoggedIn.value) {
    try {
      const res = await api.getCurrentUser()
      if (res.data && res.data.data && res.data.data.user) {
        currentUser.value = res.data.data.user
      }
    } catch (error) {
      console.error('获取用户信息失败', error)
    }
  }
}

// 监听路由变化，确保权限
watch(() => route.path, (newPath) => {
  if (isLoggedIn.value) {
    // 确保在路由变化时获取用户信息
    if (!currentUser.value) {
      fetchCurrentUser()
    }
    
    // 如果是管理员路由，但用户不是管理员，则跳转到首页
    if ((newPath === '/users' || newPath === '/push' || newPath === '/crawlers') && !isAdmin.value) {
      router.push('/home')
    }
  }
})

const showChangePasswordDialog = () => {
  passwordForm.value = {
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  }
  changePasswordDialogVisible.value = true
}

const handleChangePassword = async () => {
  if (!passwordFormRef.value) return
  
  passwordFormRef.value.validate(async (valid) => {
    if (valid) {
      changingPassword.value = true
      try {
        await api.changePassword({
          oldPassword: passwordForm.value.oldPassword,
          newPassword: passwordForm.value.newPassword
        })
        ElMessage.success('密码修改成功')
        changePasswordDialogVisible.value = false
      } catch (error) {
        console.error('修改密码失败', error)
      } finally {
        changingPassword.value = false
      }
    }
  })
}

// 添加存储事件监听器
onMounted(() => {
  fetchCurrentUser()
  window.addEventListener('storage', updateLoginState)
})

// 组件卸载时移除事件监听器
onUnmounted(() => {
  window.removeEventListener('storage', updateLoginState)
})
</script>

<style>
html, body {
  margin: 0;
  padding: 0;
  font-family: Arial, sans-serif;
  height: 100%;
  width: 100%;
}

#app {
  height: 100%;
  width: 100%;
}

.app-container {
  height: 100%;
  width: 100%;
}

.el-container {
  height: 100%;
}

.el-aside {
  background-color: #304156;
  color: #bfcbd9;
  height: 100%;
  overflow-y: auto;
  width: 220px !important;
}

.logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #263445;
  color: #fff;
}

.logo h1 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.el-menu-vertical {
  border-right: none;
}

.el-header {
  background-color: #fff;
  color: #333;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  z-index: 10;
  padding: 0 20px;
  height: 60px;
}

.header-content {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-title h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.el-main {
  background-color: #f0f2f5;
  padding: 20px;
  overflow-y: auto;
}
</style> 