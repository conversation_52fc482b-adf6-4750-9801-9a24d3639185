package service

import (
	"sync"
	"time"

	"gorm.io/gorm"
	"vulnerability_push/internal/models"
)

// ThreatIntelligenceManager 威胁情报服务管理器
type ThreatIntelligenceManager struct {
	db                *gorm.DB
	tjunService       *TJUNIntelligenceService
	weibuService      *WeibuIntelligenceService
	mutex             sync.RWMutex
}

var (
	globalThreatIntelligenceManager *ThreatIntelligenceManager
	threatIntelligenceOnce          sync.Once
)

// InitGlobalThreatIntelligenceManager 初始化全局威胁情报管理器
func InitGlobalThreatIntelligenceManager(db *gorm.DB) {
	threatIntelligenceOnce.Do(func() {
		globalThreatIntelligenceManager = NewThreatIntelligenceManager(db)
	})
}

// GetGlobalThreatIntelligenceManager 获取全局威胁情报管理器实例
func GetGlobalThreatIntelligenceManager() *ThreatIntelligenceManager {
	return globalThreatIntelligenceManager
}

// NewThreatIntelligenceManager 创建威胁情报服务管理器
func NewThreatIntelligenceManager(db *gorm.DB) *ThreatIntelligenceManager {
	manager := &ThreatIntelligenceManager{
		db: db,
	}

	// 初始化服务实例
	manager.initializeServices()

	return manager
}

// GetTJUNService 获取天际友盟威胁情报服务
func (m *ThreatIntelligenceManager) GetTJUNService() *TJUNIntelligenceService {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.tjunService
}

// GetWeibuService 获取微步威胁情报服务
func (m *ThreatIntelligenceManager) GetWeibuService() *WeibuIntelligenceService {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.weibuService
}

// initializeServices 初始化威胁情报服务
func (m *ThreatIntelligenceManager) initializeServices() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 初始化天际友盟服务
	m.tjunService = m.createTJUNService()

	// 初始化微步服务
	m.weibuService = NewWeibuIntelligenceService(m.db)
}

// createTJUNService 创建天际友盟服务
func (m *ThreatIntelligenceManager) createTJUNService() *TJUNIntelligenceService {
	// 从数据库获取天际友盟配置
	var interface_ models.ThreatIntelligenceInterface
	err := m.db.Where("type = ? AND status = ?", "tjun", "enabled").First(&interface_).Error

	var config TJUNConfig
	if err != nil {
		// 如果没有找到配置，使用默认配置
		config = GetDefaultTJUNConfig()
	} else {
		// 解析数据库中的配置
		tjunConfig, parseErr := interface_.GetTJUNConfig()
		if parseErr != nil {
			// 解析失败，使用默认配置
			config = GetDefaultTJUNConfig()
		} else {
			config = TJUNConfig{
				Host:      tjunConfig.Host,
				AppKey:    tjunConfig.AppKey,
				AppSecret: tjunConfig.AppSecret,
				Token:     tjunConfig.Token,
				Timeout:   time.Duration(tjunConfig.Timeout) * time.Second,
			}
		}
	}

	return NewTJUNIntelligenceService(m.db, config)
}

// RefreshServices 刷新威胁情报服务配置
func (m *ThreatIntelligenceManager) RefreshServices() {
	m.initializeServices()
}

// UpdateTJUNConfig 更新天际友盟配置（保持向后兼容）
func (m *ThreatIntelligenceManager) UpdateTJUNConfig(config TJUNConfig) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.tjunService = NewTJUNIntelligenceService(m.db, config)
}

// BatchUpdateAllUnqueriedIOCs 批量更新所有未查询的IOC记录
func (m *ThreatIntelligenceManager) BatchUpdateAllUnqueriedIOCs() (map[string]int, error) {
	results := make(map[string]int)

	// 更新天际友盟数据
	tjunCount, tjunErr := m.tjunService.BatchUpdateUnqueriedIOCs()
	results["tjun_success_count"] = tjunCount
	if tjunErr != nil {
		results["tjun_error"] = 1
	}

	// 更新微步数据
	weibuCount, weibuErr := m.weibuService.BatchUpdateUnqueriedIOCs()
	results["weibu_success_count"] = weibuCount
	if weibuErr != nil {
		results["weibu_error"] = 1
	}

	// 如果两个服务都出错，返回错误
	if tjunErr != nil && weibuErr != nil {
		return results, tjunErr // 返回第一个错误
	}

	return results, nil
}

// GetServiceStatus 获取威胁情报服务状态
func (m *ThreatIntelligenceManager) GetServiceStatus() map[string]interface{} {
	return map[string]interface{}{
		"tjun_service": map[string]interface{}{
			"enabled":         true,
			"supported_types": []string{"ip", "domain", "url", "hash"},
		},
		"weibu_service": map[string]interface{}{
			"enabled":         true,
			"supported_types": m.weibuService.GetSupportedTypes(),
		},
	}
}

// 全局便捷函数

// GetGlobalTJUNService 获取全局天际友盟威胁情报服务
func GetGlobalTJUNService() *TJUNIntelligenceService {
	if globalThreatIntelligenceManager == nil {
		return nil
	}
	return globalThreatIntelligenceManager.GetTJUNService()
}

// GetGlobalWeibuService 获取全局微步威胁情报服务
func GetGlobalWeibuService() *WeibuIntelligenceService {
	if globalThreatIntelligenceManager == nil {
		return nil
	}
	return globalThreatIntelligenceManager.GetWeibuService()
}
